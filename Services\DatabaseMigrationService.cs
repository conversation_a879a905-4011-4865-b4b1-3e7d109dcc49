using System;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;

namespace SFDSystem.Services
{
    /// <summary>
    /// خدمة تطبيق تحديثات قاعدة البيانات
    /// </summary>
    public class DatabaseMigrationService
    {
        private readonly string _connectionString;

        public DatabaseMigrationService(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// تطبيق تحديث عمود VisitNumber
        /// </summary>
        public async Task<bool> ApplyVisitNumberMigrationAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // التحقق من وجود العمود
                var checkColumnSql = @"
                    SELECT COUNT(*) 
                    FROM sys.columns 
                    WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') 
                    AND name = 'VisitNumber'";

                using var checkCommand = new SqlCommand(checkColumnSql, connection);
                var columnExists = (int)await checkCommand.ExecuteScalarAsync() > 0;

                if (columnExists)
                {
                    System.Diagnostics.Debug.WriteLine("✅ العمود VisitNumber موجود بالفعل");
                    return true;
                }

                System.Diagnostics.Debug.WriteLine("🔄 بدء تطبيق تحديث VisitNumber...");

                // إضافة العمود
                var addColumnSql = @"
                    ALTER TABLE [dbo].[DriverQuotes]
                    ADD [VisitNumber] NVARCHAR(50) NULL";

                using var addCommand = new SqlCommand(addColumnSql, connection);
                await addCommand.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود VisitNumber");

                // إنشاء فهرس
                var createIndexSql = @"
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'IX_DriverQuotes_VisitNumber')
                    BEGIN
                        CREATE NONCLUSTERED INDEX [IX_DriverQuotes_VisitNumber] 
                        ON [dbo].[DriverQuotes] ([VisitNumber])
                    END";

                using var indexCommand = new SqlCommand(createIndexSql, connection);
                await indexCommand.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء الفهرس");

                // تحديث البيانات الموجودة
                var updateDataSql = @"
                    UPDATE [dbo].[DriverQuotes] 
                    SET [VisitNumber] = 
                        CASE 
                            WHEN [Notes] LIKE 'Visit:%' THEN 
                                LTRIM(RTRIM(SUBSTRING([Notes], 7, CHARINDEX('-', [Notes] + '-', 7) - 7)))
                            WHEN [Notes] LIKE '%911-%' THEN
                                LTRIM(RTRIM(SUBSTRING([Notes], CHARINDEX('911-', [Notes]), 10)))
                            ELSE NULL
                        END
                    WHERE [Notes] IS NOT NULL 
                      AND ([Notes] LIKE 'Visit:%' OR [Notes] LIKE '%911-%')
                      AND [VisitNumber] IS NULL";

                using var updateCommand = new SqlCommand(updateDataSql, connection);
                var updatedRows = await updateCommand.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث {updatedRows} سجل");

                // عرض إحصائيات
                var statsSql = @"
                    SELECT 
                        COUNT(*) AS TotalRecords,
                        COUNT([VisitNumber]) AS RecordsWithVisitNumber,
                        COUNT(*) - COUNT([VisitNumber]) AS RecordsWithoutVisitNumber
                    FROM [dbo].[DriverQuotes]";

                using var statsCommand = new SqlCommand(statsSql, connection);
                using var reader = await statsCommand.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    var total = reader.GetInt32(0);
                    var withVisit = reader.GetInt32(1);
                    var withoutVisit = reader.GetInt32(2);

                    System.Diagnostics.Debug.WriteLine($"📊 إحصائيات: إجمالي {total}، مع رقم الزيارة {withVisit}، بدون رقم الزيارة {withoutVisit}");
                }

                System.Diagnostics.Debug.WriteLine("🎉 تم تطبيق التحديث بنجاح!");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق التحديث: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من حالة قاعدة البيانات
        /// </summary>
        public async Task<bool> CheckDatabaseStatusAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var checkSql = @"
                    SELECT 
                        CASE WHEN EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[DriverQuotes]') AND name = 'VisitNumber')
                             THEN 1 ELSE 0 END AS VisitNumberExists";

                using var command = new SqlCommand(checkSql, connection);
                var visitNumberExists = (int)await command.ExecuteScalarAsync() == 1;

                System.Diagnostics.Debug.WriteLine($"🔍 حالة قاعدة البيانات - عمود VisitNumber: {(visitNumberExists ? "موجود" : "غير موجود")}");

                return visitNumberExists;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص قاعدة البيانات: {ex.Message}");
                return false;
            }
        }
    }
}
